/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Button } from '@/components/ui/button';
import { DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAppDispatch } from '@/hooks';
import { useCreateBulkEmployeeMutation } from '@/services/employee.service';
import { modal } from '@/store/module/modal';
import { ColDef } from 'ag-grid-community';
import { Trash2Icon } from 'lucide-react';
import Papa from 'papaparse';
import { useRef, useState } from 'react';
import { toast } from 'sonner';
import BaseTable from '../table/BaseTable';
import { Input } from '../ui/input';

const columnFields = [
  'staffcode',
  'title',
  'first_name',
  'last_name',
  'birthday',
  'gender',
  'mstatus',
  'nationality',
  'state_of_origin',
  'lga_origin',
  'residential_address',
  'residential_city',
  'residential_state',
  'resident_country',
  'phone_1',
  'phone_2',
  'email_address',
  'next_of_kin',
  'next_of_kin_relationship',
  'next_of_kin_address',
  'next_of_kin_phone',
  'gradelevel',
  'job_title',
  'qualification',
  'job_grade',
  'job_unit',
  'job_dept',
  'job_location',
  'date_employed',
  'bank_name', // Will be excluded from backend submission
  'bank_sortcode', // Will be excluded from backend submission
  'bank_accountno',
  'Taxid',
  'Pensionid',
  'PFA',
  'date_appointed_to_level',
  'middle_name',
  'place_of_birth',
  'religion',
  'next_of_kin_email',
  'course',
  'institution_name',
  'date_of_graduation',
  'contract_type',
  'bvn',
  'nin',
  'name_of_spouse',
  'no_of_children',
  'passport',
  'certificate',
  'guarantor_passport',
  'guarantor_fullname',
  'guarantor_phone',
  'guarantor_relationship',
  'guarantor_address',
  'guarantor_occupation',
  'guarantor_means_of_identification',
  'salary_package',
];

const requiredFields = [
  'staffcode',
  'title',
  'first_name',
  'last_name',
  'birthday',
  'gender',
  'state_of_origin',
  'lga_origin',
  'residential_address',
  'residential_city',
  'residential_state',
  'resident_country',
  'phone_1',
  'email_address',
  'place_of_birth',
  'religion',
  'next_of_kin',
  'next_of_kin_relationship',
  'next_of_kin_phone',
  'next_of_kin_address',
  'bvn',
  'nin',
  'job_location',
  'job_title',
  'salary_package',
];

// Fields to exclude when sending to backend
const excludedFields = ['bank_name', 'bank_sortcode'];

// Header mapping for converting new CSV headers to original backend format
const headerMapping: Record<string, string> = {
  staffcode: 'staffCode',
  title: 'title',
  first_name: 'firstName',
  last_name: 'lastName',
  middle_name: 'middleName',
  birthday: 'birthday',
  gender: 'gender',
  mstatus: 'maritalStatus',
  nationality: 'nationality',
  state_of_origin: 'stateOfOrigin',
  lga_origin: 'localGovt',
  residential_address: 'residentialAddress',
  residential_city: 'residentialLocalGovt',
  residential_state: 'residentialState',
  resident_country: 'residentialCountry',
  phone_1: 'phone1',
  phone_2: 'phone2',
  email_address: 'email',
  place_of_birth: 'placeOfBirth',
  religion: 'religion',
  next_of_kin: 'nextOfKinFullName',
  next_of_kin_relationship: 'nextOfKinRelationship',
  next_of_kin_phone: 'nextOfKinPhoneNumber',
  next_of_kin_email: 'nextOfKinEmail',
  next_of_kin_address: 'nextOfKinAddress',
  qualification: 'highestQualification',
  course: 'course',
  institution_name: 'institutionName',
  date_of_graduation: 'dateOfGraduation',
  date_employed: 'dateEmployed',
  contract_type: 'contractTypeName',
  bvn: 'bvn',
  nin: 'nin',
  name_of_spouse: 'nameOfSpouse',
  no_of_children: 'noOfChildren',
  bank_accountno: 'accountNumber',
  Taxid: 'taxId',
  Pensionid: 'pensionId',
  PFA: 'pfa',
  date_appointed_to_level: 'dateAppointedToLevel',
  passport: 'passport',
  certificate: 'certificate',
  guarantor_passport: 'guarantorPassport',
  guarantor_fullname: 'guarantorFullname',
  guarantor_phone: 'guarantorPhoneNumber',
  guarantor_relationship: 'guarantorRelationShip',
  guarantor_address: 'guarantorAddress',
  guarantor_occupation: 'guarantorOccupation',
  guarantor_means_of_identification: 'guarantorMeansOfIdentification',
  gradelevel: 'gradeLevelName',
  job_title: 'jobTitleName',
  job_grade: 'jobGradeName',
  job_unit: 'unitName',
  job_dept: 'departmentName',
  job_location: 'branchName',
  salary_package: 'salaryPackageName',
};

const EmployeeBulkUploadModal = () => {
  const dispatch = useAppDispatch();

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [employeeData, setEmployeeData] = useState<any[]>([]);

  const [createBulkEmployee, { isLoading }] = useCreateBulkEmployeeMutation();

  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map(key => ({
    headerName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: result => {
        const rawRows = result.data as any[];

        // Check if the uploaded file contains all required columns
        const uploadedHeaders = result.meta.fields ?? [];

        // Check for missing required headers
        const missingHeaders = requiredFields.filter(field => !uploadedHeaders.includes(field));

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(', ')}`);
          return;
        }

        // Filter out completely empty rows
        const rows = rawRows.filter(row => {
          return columnFields.some(field => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== '';
          });
        });

        if (!rows.length) {
          toast.error('No valid data rows found in CSV');
          return;
        }
        setEmployeeData(rows);
      },
    });
  };

  const transformDataForBackend = (data: any[]) => {
    return data.map(row => {
      const transformedRow: any = {};

      Object.keys(row).forEach(key => {
        // Skip excluded fields
        if (excludedFields.includes(key)) {
          return;
        }

        // Map to backend field name
        const backendKey = headerMapping[key] || key;
        transformedRow[backendKey] = row[key];
      });

      return transformedRow;
    });
  };

  const handleSubmit = async () => {
    if (!employeeData.length) {
      toast.error('No data to upload');
      return;
    }

    // Transform data to match backend expectations
    const transformedData = transformDataForBackend(employeeData);
    console.log(transformedData);

    const res = await createBulkEmployee({ employees: transformedData }).unwrap();

    if (res.success) {
      toast(res.message);
      dispatch(modal.mutation.close());
    }

    toast('Data uploaded successfully!');

    setEmployeeData([]);
  };

  const handleClose = () => {
    setEmployeeData([]);
    setError(undefined);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...employeeData];
    updatedData.splice(rowIndex, 1);
    setEmployeeData(updatedData);
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/templates/create_employee_template.csv';
    link.setAttribute('download', 'create-employee-template.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Employee Bulk Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <div>
            <p>Upload a `.csv` file with employee information.</p>
          </div>
          <div className="flex flex-col gap-4 mt-2 pb-2 z-40">
            <Input
              type="file"
              accept=".csv"
              ref={fileInputRef}
              // className="hidden"
              onChange={handleFileUpload}
              disabled={isLoading}
            />
            <Button onClick={handleDownloadTemplate} variant="link" className="px-0 cursor-pointer w-max">
              Download Template
            </Button>
          </div>
        </DialogDescription>
      </DialogHeader>

      <div className="w-full">
        {error && (
          <div>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
        {employeeData.length > 0 && colDefs.length > 0 && (
          <div className="mt-4 max-h-[400px] overflow-auto border rounded">
            <BaseTable
              rowData={employeeData}
              columnDefs={colDefs}
              setPaginationPageSize={() => {}}
              showCustomPagination={false}
              actionOptions={{
                showDefault: true,
                actions: [
                  {
                    title: 'Delete',
                    Icon: Trash2Icon,
                    onClick: node => {
                      handleDelete(node.rowIndex!);
                    },
                  },
                ],
              }}
            />
          </div>
        )}
      </div>

      {employeeData.length > 0 && colDefs.length > 0 && (
        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" disabled={isLoading} size="sm" onClick={handleClose}>
            Cancel
          </Button>
          <Button disabled={isLoading} size="sm" onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmployeeBulkUploadModal;
