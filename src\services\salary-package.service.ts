// services/authApi.ts

import { CreateSalaryPackageRequestDef } from '@/models/request/salary-package/create-salary-package.request';
import { DefaultResponse } from '@/models/response/default.response';
import { SalaryPackageResponseDef } from '@/models/response/salary-package';
import { baseService } from './base.service';

export const salaryPackageService = baseService.injectEndpoints({
  endpoints: builder => ({
    createSalaryPackage: builder.mutation<DefaultResponse, CreateSalaryPackageRequestDef>({
      query: credentials => ({
        url: '/salary-package/create',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['SalaryPackages'],
    }),
    createBulkSalaryPackage: builder.mutation<DefaultResponse, { salaryPackages: CreateSalaryPackageRequestDef[] }>({
      query: credentials => ({
        url: '/salary-package/create/bulk',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['SalaryPackages'],
    }),
    getSalaryPackages: builder.query<DefaultResponse & { data: SalaryPackageResponseDef[] }, void>({
      query: () => ({
        url: '/salary-package/read',
        method: 'GET',
      }),
      providesTags: ['SalaryPackages'],
    }),
  }),
  overrideExisting: false,
});

export const { useCreateSalaryPackageMutation, useCreateBulkSalaryPackageMutation, useGetSalaryPackagesQuery } = salaryPackageService;
