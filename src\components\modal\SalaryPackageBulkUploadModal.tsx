/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Button } from '@/components/ui/button';
import { DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAppDispatch } from '@/hooks';
import { useCreateBulkSalaryPackageMutation } from '@/services/salary-package.service';
import { modal } from '@/store/module/modal';
import { ColDef } from 'ag-grid-community';
import { Trash2Icon } from 'lucide-react';
import Papa from 'papaparse';
import { useRef, useState } from 'react';
import { toast } from 'sonner';
import BaseTable from '../table/BaseTable';
import { Input } from '../ui/input';

const columnFields = [
  'name',
  'description',
  'baseSalary',
  'pensionRate',
  'nhfRate',
  'taxAmount',
  'currency',
  'apprenticeAllowance',
  'housingAllowance',
  'transportAllowance',
  'utilityAllowance',
  'selfMaintenanceAllowance',
  'hazardOrEntertainmentAllowance',
  'furnitureAllowance',
  'fuelSubsidy',
  'domesticStaffAllowance',
  'childEducationSubsidy',
  'levelProficiencyAllowance',
  'responsibilityAllowance',
  'monthlyGrossSalary',
  'annualGrossSalary',
];

const requiredFields = ['name', 'baseSalary', 'currency'];

// Header mapping for converting CSV headers to backend format
const headerMapping: Record<string, string> = {
  name: 'name',
  description: 'description',
  baseSalary: 'baseSalary',
  pensionRate: 'pensionRate',
  nhfRate: 'nhfRate',
  taxAmount: 'taxAmount',
  currency: 'currency',
  apprenticeAllowance: 'apprenticeAllowance',
  housingAllowance: 'housingAllowance',
  transportAllowance: 'transportAllowance',
  utilityAllowance: 'utilityAllowance',
  selfMaintenanceAllowance: 'selfMaintenanceAllowance',
  hazardOrEntertainmentAllowance: 'hazardOrEntertainmentAllowance',
  furnitureAllowance: 'furnitureAllowance',
  fuelSubsidy: 'fuelSubsidy',
  domesticStaffAllowance: 'domesticStaffAllowance',
  childEducationSubsidy: 'childEducationSubsidy',
  levelProficiencyAllowance: 'levelProficiencyAllowance',
  responsibilityAllowance: 'responsibilityAllowance',
  monthlyGrossSalary: 'monthlyGrossSalary',
  annualGrossSalary: 'annualGrossSalary',
};

const SalaryPackageBulkUploadModal = () => {
  const dispatch = useAppDispatch();

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [salaryPackageData, setSalaryPackageData] = useState<any[]>([]);

  const [createBulkSalaryPackage, { isLoading }] = useCreateBulkSalaryPackageMutation();

  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map(key => ({
    headerName: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: result => {
        const rawRows = result.data as any[];

        // Check if the uploaded file contains all required columns
        const uploadedHeaders = result.meta.fields ?? [];

        // Check for missing required headers
        const missingHeaders = requiredFields.filter(field => !uploadedHeaders.includes(field));

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(', ')}`);
          return;
        }

        // Filter out completely empty rows
        const rows = rawRows.filter(row => {
          return columnFields.some(field => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== '';
          });
        });

        if (!rows.length) {
          toast.error('No valid data rows found in CSV');
          return;
        }
        setSalaryPackageData(rows);
      },
    });
  };

  const transformDataForBackend = (data: any[]) => {
    return data.map(row => {
      const transformedRow: any = {};

      Object.keys(row).forEach(key => {
        // Map to backend field name
        const backendKey = headerMapping[key] || key;
        const value = row[key];

        // Convert numeric fields to numbers
        if (
          [
            'baseSalary',
            'pensionRate',
            'nhfRate',
            'taxAmount',
            'apprenticeAllowance',
            'housingAllowance',
            'transportAllowance',
            'utilityAllowance',
            'selfMaintenanceAllowance',
            'hazardOrEntertainmentAllowance',
            'furnitureAllowance',
            'fuelSubsidy',
            'domesticStaffAllowance',
            'childEducationSubsidy',
            'levelProficiencyAllowance',
            'responsibilityAllowance',
            'monthlyGrossSalary',
            'annualGrossSalary',
          ].includes(key)
        ) {
          transformedRow[backendKey] = Number(String(value).replace(/,/g, '')) || 0;
        } else {
          transformedRow[backendKey] = value;
        }
      });

      return transformedRow;
    });
  };

  const handleSubmit = async () => {
    if (!salaryPackageData.length) {
      toast.error('No data to upload');
      return;
    }

    // Transform data to match backend expectations
    const transformedData = transformDataForBackend(salaryPackageData);
    console.log('Transformed salary package data:', transformedData);

    try {
      // TODO: Uncomment when API is ready
      // const res = await createBulkSalaryPackage({ salaryPackages: transformedData }).unwrap();

      // if (res.success) {
      //   toast.success(res.message);
      //   dispatch(modal.mutation.close());
      // }

      // Temporary success message for testing
      toast.success('Salary packages uploaded successfully!');
      dispatch(modal.mutation.close());
      setSalaryPackageData([]);
    } catch (error) {
      console.error('Error uploading salary packages:', error);
      toast.error('Failed to upload salary packages');
    }
  };

  const handleClose = () => {
    setSalaryPackageData([]);
    setError(undefined);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...salaryPackageData];
    updatedData.splice(rowIndex, 1);
    setSalaryPackageData(updatedData);
  };

  const handleDownloadTemplate = () => {
    const headers = columnFields.join(',');
    const sampleRow = [
      'Sample Package',
      'Sample description',
      '100000',
      '8000',
      '2500',
      '15000',
      'NGN',
      '5000',
      '25000',
      '10000',
      '5000',
      '3000',
      '2000',
      '8000',
      '15000',
      '5000',
      '10000',
      '3000',
      '5000',
      '150000',
      '1800000',
    ].join(',');

    const csvContent = `${headers}\n${sampleRow}`;
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'salary-package-template.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Salary Package Bulk Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <div>
            <p>Upload a `.csv` file with salary package information.</p>
          </div>
          <div className="flex flex-col gap-4 mt-2 pb-2 z-40">
            <Input type="file" accept=".csv" ref={fileInputRef} onChange={handleFileUpload} disabled={isLoading} />
            <Button onClick={handleDownloadTemplate} variant="link" className="px-0 cursor-pointer w-max">
              Download Template
            </Button>
          </div>
        </DialogDescription>
      </DialogHeader>

      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

      {salaryPackageData.length > 0 && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Preview ({salaryPackageData.length} salary packages)</h3>
            <Button onClick={handleSubmit} disabled={isLoading} className="bg-green-600 hover:bg-green-700">
              {isLoading ? 'Uploading...' : 'Upload Salary Packages'}
            </Button>
          </div>

          <div className="max-h-96 overflow-auto">
            <BaseTable
              rowData={salaryPackageData}
              columnDefs={[
                ...colDefs,
                {
                  headerName: 'Actions',
                  field: 'actions',
                  cellRenderer: (params: any) => (
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(params.node.rowIndex)} className="text-red-600 hover:text-red-800">
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  ),
                  width: 100,
                  pinned: 'right',
                },
              ]}
              onClose={handleClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SalaryPackageBulkUploadModal;
