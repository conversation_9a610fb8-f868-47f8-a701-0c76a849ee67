/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Button } from '@/components/ui/button';
import { DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAppDispatch } from '@/hooks';
import { useCreateBulkSalaryPackageMutation } from '@/services/salary-package.service';
import { modal } from '@/store/module/modal';
import { ColDef } from 'ag-grid-community';
import { Trash2Icon } from 'lucide-react';
import Papa from 'papaparse';
import { useRef, useState } from 'react';
import { toast } from 'sonner';
import BaseTable from '../table/BaseTable';
import { Input } from '../ui/input';

const columnFields = [
  'Grade level',
  ' Salary Package',
  'Grade Level Description',
  // 'pension_rate',
  // 'nhf_rate',
  // 'tax_amount',
  // 'currency',
  'Basic Salary',
  'Apprentice Allowance',
  'Housing Allowance',
  'Transport Allowance',
  'Utility Allowance',
  'Self Maintenance Allowance',
  'Hazard (OPS) or Entertainment Allowance (ADM)',
  'Furniture Allowance',
  'Fuel Subsidy',
  'Domestic Staff Allowance',
  'Child Education Subsidy',
  'Level Proficiency Allowance',
  'Responsibility Allowance',
  'Monthly Gross Salary',
  'Annual Gross Salary',
];

const requiredFields = ['Grade level', 'Salary Package'];
// const requiredFields = ['Grade level Salary Package', 'Basic Salary', 'Currency'];

// Header mapping for converting CSV headers to backend format
const headerMapping: Record<string, string> = {
  'Grade level Salary Package': 'name',
  'Grade Level Description': 'description',
  'Basic Salary': 'baseSalary',
  'Apprentice Allowance': 'apprenticeAllowance',
  'Housing Allowance': 'housingAllowance',
  'Transport Allowance': 'transportAllowance',
  'Utility Allowance': 'utilityAllowance',
  'Self Maintenance Allowance': 'selfMaintenanceAllowance',
  'Hazard (OPS) or Entertainment Allowance (ADM)': 'hazardOrEntertainmentAllowance',
  'Furniture Allowance': 'furnitureAllowance',
  'Fuel Subsidy': 'fuelSubsidy',
  'Domestic Staff Allowance': 'domesticStaffAllowance',
  'Child Education Subsidy': 'childEducationSubsidy',
  'Level Proficiency Allowance': 'levelProficiencyAllowance',
  'Responsibility Allowance': 'responsibilityAllowance',
  'Monthly Gross Salary': 'monthlyGrossSalary',
  'Annual Gross Salary': 'annualGrossSalary',
};

const SalaryPackageBulkUploadModal = () => {
  const dispatch = useAppDispatch();

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [salaryPackageData, setSalaryPackageData] = useState<any[]>([]);

  const [createBulkSalaryPackage, { isLoading }] = useCreateBulkSalaryPackageMutation();

  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map(key => ({
    headerName: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: result => {
        const rawRows = result.data as any[];
        console.log('rows:', rawRows);

        // Check if the uploaded file contains all required columns
        const uploadedHeaders = result.meta.fields ?? [];
        console.log('headers', uploadedHeaders);

        // Check for missing required headers
        const missingHeaders = requiredFields.filter(field => !uploadedHeaders.includes(field));

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(', ')}`);
          return;
        }

        // Filter out completely empty rows
        const rows = rawRows.filter(row => {
          return columnFields.some(field => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== '';
          });
        });

        if (!rows.length) {
          toast.error('No valid data rows found in CSV');
          return;
        }
        console.log('rows:', rows);
        setSalaryPackageData(rows);
      },
    });
  };

  const transformDataForBackend = (data: any[]) => {
    return data.map(row => {
      const transformedRow: any = {};

      Object.keys(row).forEach(key => {
        const backendKey = headerMapping[key] || key;
        const value = row[key];

        if (
          [
            'Basic Salary',
            // 'pension_rate',
            // 'nhf_rate',
            // 'tax_amount',
            'Apprentice Allowance',
            'Housing Allowance',
            'Transport Allowance',
            'Utility Allowance',
            'Self Maintenance Allowance',
            'Hazard (OPS) or Entertainment Allowance (ADM)',
            'Furniture Allowance',
            'Fuel Subsidy',
            'Domestic Staff Allowance',
            'Child Education Subsidy',
            'Level Proficiency Allowance',
            'Responsibility Allowance',
            'Monthly Gross Salary',
            'Annual Gross Salary',
          ].includes(key)
        ) {
          transformedRow[backendKey] = Number(String(value).replaceAll(',', '')) || 0;
        } else {
          transformedRow[backendKey] = value;
        }
      });

      return transformedRow;
    });
  };

  const handleSubmit = async () => {
    if (!salaryPackageData.length) {
      toast.error('No data to upload');
      return;
    }

    // Transform data to match backend expectations
    const transformedData = transformDataForBackend(salaryPackageData);
    console.log('Transformed salary package data:', transformedData);

    try {
      // TODO: Uncomment when API is ready
      // const res = await createBulkSalaryPackage({ salaryPackages: transformedData }).unwrap();

      // if (res.success) {
      //   toast.success(res.message);
      //   dispatch(modal.mutation.close());
      // }

      // Temporary success message for testing
      toast.success('Salary packages uploaded successfully!');
      dispatch(modal.mutation.close());
      setSalaryPackageData([]);
    } catch (error) {
      console.error('Error uploading salary packages:', error);
      toast.error('Failed to upload salary packages');
    }
  };

  const handleClose = () => {
    setSalaryPackageData([]);
    setError(undefined);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...salaryPackageData];
    updatedData.splice(rowIndex, 1);
    setSalaryPackageData(updatedData);
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/templates/salary-package-template.csv';
    link.setAttribute('download', 'salary-package-template.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Salary Package Bulk Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <div>
            <p>Upload a `.csv` file with salary package information.</p>
          </div>
          <div className="flex flex-col gap-4 mt-2 pb-2 z-40">
            <Input type="file" accept=".csv" ref={fileInputRef} onChange={handleFileUpload} disabled={isLoading} />
            <Button onClick={handleDownloadTemplate} variant="link" className="px-0 cursor-pointer w-max">
              Download Template
            </Button>
          </div>
        </DialogDescription>
      </DialogHeader>

      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

      <div className="w-full">
        {error && (
          <div>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
        {salaryPackageData.length > 0 && colDefs.length > 0 && (
          <div className="mt-4 max-h-[400px] overflow-auto border rounded">
            <BaseTable
              rowData={salaryPackageData}
              columnDefs={colDefs}
              setPaginationPageSize={() => {}}
              showCustomPagination={false}
              actionOptions={{
                showDefault: true,
                actions: [
                  {
                    title: 'Delete',
                    Icon: Trash2Icon,
                    onClick: node => {
                      handleDelete(node.rowIndex!);
                    },
                  },
                ],
              }}
            />
          </div>
        )}

        {salaryPackageData.length > 0 && colDefs.length > 0 && (
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" disabled={isLoading} size="sm" onClick={handleClose}>
              Cancel
            </Button>
            <Button disabled={isLoading} size="sm" onClick={handleSubmit}>
              Submit
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalaryPackageBulkUploadModal;
